import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:railops/services/attendance_services/location_service.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/models/notification_tray_model.dart';

/// Integration service that connects train location API with notification tray
/// Handles the complete flow from API call to notification display
class TrainLocationIntegrationService {
  static final TrainLocationIntegrationService _instance =
      TrainLocationIntegrationService._internal();

  factory TrainLocationIntegrationService() => _instance;
  TrainLocationIntegrationService._internal();

  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static bool _isInitialized = false;

  /// Initialize local notifications
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      const androidSettings = AndroidInitializationSettings('ic_notification');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initializationSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          if (kDebugMode) {
            print('Notification tapped: ${response.payload}');
          }
          // Handle notification tap if needed
        },
      );

      // Create notification channel for Android
      const androidChannel = AndroidNotificationChannel(
        'train_location_channel',
        'Train Location Updates',
        description: 'Notifications for train location and passenger updates',
        importance: Importance.high,
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(androidChannel);

      _isInitialized = true;

      if (kDebugMode) {
        print(
            '✅ TrainLocationIntegrationService: Local notifications initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            '❌ TrainLocationIntegrationService: Failed to initialize notifications: $e');
      }
    }
  }

  /// Fetch train location data and update notification tray
  /// This is the main integration method that should be called periodically
  static Future<bool> fetchAndUpdateNotifications({
    required String trainNumber,
    required String date,
    required NotificationTrayProvider trayProvider,
    bool enableProximityCheck = true,
    bool showPushNotifications = true,
  }) async {
    try {
      if (kDebugMode) {
        print(
            '🔄 TrainLocationIntegrationService: Starting fetch and update...');
      }

      // Ensure notifications are initialized
      await initialize();

      // Get current position if proximity check is enabled
      Position? currentPosition;
      if (enableProximityCheck) {
        try {
          currentPosition = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
            ),
          );
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Could not get current position: $e');
          }
          // Continue without position
        }
      }

      // Fetch train location data
      final response = await TrainLocationNotificationService
          .fetchTrainLocationWithNotifications(
        trainNumber: trainNumber,
        date: date,
        currentPosition: currentPosition,
      );

      if (response == null) {
        if (kDebugMode) {
          print('ℹ️ No train location data received');
        }
        return false;
      }

      // Create notification tray items from response
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        response,
        trainNumber,
        date,
      );

      if (trayItems.isEmpty) {
        if (kDebugMode) {
          print('ℹ️ No notification tray items created');
        }
        return false;
      }

      // Add items to notification tray
      await trayProvider.addItems(trayItems);

      // Show push notification if enabled and there are new items
      if (showPushNotifications && trayItems.isNotEmpty) {
        await _showPushNotification(trayItems, response);
      }

      if (kDebugMode) {
        print(
            '✅ TrainLocationIntegrationService: Successfully updated ${trayItems.length} items');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print(
            '❌ TrainLocationIntegrationService: Error in fetch and update: $e');
      }
      return false;
    }
  }

  /// Show local notification for new train location updates
  static Future<void> _showLocalNotification({
    required String title,
    required String body,
    Map<String, String>? payload,
  }) async {
    try {
      // Ensure notifications are initialized
      if (!_isInitialized) {
        await initialize();
      }

      const androidDetails = AndroidNotificationDetails(
        'train_location_channel',
        'Train Location Updates',
        channelDescription:
            'Notifications for train location and passenger updates',
        importance: Importance.high,
        priority: Priority.high,
        icon: 'ic_notification',
      );

      const iosDetails = DarwinNotificationDetails();

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        notificationDetails,
        payload: payload?.toString(),
      );

      if (kDebugMode) {
        print('🔔 Local notification shown: $title - $body');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error showing local notification: $e');
      }
    }
  }

  /// Show push notification for new train location updates
  static Future<void> _showPushNotification(
    List<NotificationTrayItem> items,
    dynamic response,
  ) async {
    try {
      // Group items by station for summary
      final stationGroups = <String, List<NotificationTrayItem>>{};
      for (final item in items) {
        stationGroups[item.stationCode] = stationGroups[item.stationCode] ?? [];
        stationGroups[item.stationCode]!.add(item);
      }

      // Create notification title and body
      final stationCodes = stationGroups.keys.toList();
      final title = stationCodes.length == 1
          ? 'Approaching ${stationCodes.first}'
          : 'Approaching ${stationCodes.length} stations';

      final totalOnboarding =
          items.fold(0, (sum, item) => sum + item.onboardingCount);
      final totalOffboarding =
          items.fold(0, (sum, item) => sum + item.offboardingCount);
      final totalVacant = items.fold(0, (sum, item) => sum + item.vacantCount);

      final body =
          'Onboarding: $totalOnboarding, Off-boarding: $totalOffboarding, Vacant: $totalVacant';

      // Show notification using local notifications
      await _showLocalNotification(
        title: title,
        body: body,
        payload: {
          'type': 'train_location_update',
          'stations': stationCodes.join(','),
          'train_number': response.trainNumber ?? '',
          'date': response.date ?? '',
          'total_onboarding': totalOnboarding.toString(),
          'total_offboarding': totalOffboarding.toString(),
          'total_vacant': totalVacant.toString(),
        },
      );

      if (kDebugMode) {
        print('🔔 Showed push notification: $title - $body');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error showing push notification: $e');
      }
    }
  }

  /// Start periodic monitoring for a specific train
  /// This would be called when a CA starts their shift
  static Future<void> startTrainMonitoring({
    required String trainNumber,
    required String date,
    required NotificationTrayProvider trayProvider,
    Duration interval = const Duration(minutes: 5), // Check every 5 minutes
    bool enableProximityCheck = true,
    bool showPushNotifications = true,
  }) async {
    try {
      if (kDebugMode) {
        print('🚀 Starting train monitoring for $trainNumber on $date');
      }

      // Initial fetch
      await fetchAndUpdateNotifications(
        trainNumber: trainNumber,
        date: date,
        trayProvider: trayProvider,
        enableProximityCheck: enableProximityCheck,
        showPushNotifications: showPushNotifications,
      );

      // TODO: Implement periodic monitoring using WorkManager or Timer
      // This would require background processing capabilities

      if (kDebugMode) {
        print('✅ Train monitoring started successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error starting train monitoring: $e');
      }
    }
  }

  /// Stop train monitoring
  static Future<void> stopTrainMonitoring() async {
    try {
      if (kDebugMode) {
        print('🛑 Stopping train monitoring');
      }

      // TODO: Cancel periodic monitoring tasks

      if (kDebugMode) {
        print('✅ Train monitoring stopped');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error stopping train monitoring: $e');
      }
    }
  }

  /// Manual refresh - useful for pull-to-refresh functionality
  static Future<bool> manualRefresh({
    required String trainNumber,
    required String date,
    required NotificationTrayProvider trayProvider,
  }) async {
    if (kDebugMode) {
      print('🔄 Manual refresh requested');
    }

    return await fetchAndUpdateNotifications(
      trainNumber: trainNumber,
      date: date,
      trayProvider: trayProvider,
      enableProximityCheck: true,
      showPushNotifications:
          false, // Don't show push notifications for manual refresh
    );
  }

  /// Get service statistics
  static Map<String, dynamic> getServiceStats() {
    final locationStats =
        TrainLocationNotificationService().getNotificationStats();

    return {
      'service_name': 'TrainLocationIntegrationService',
      'location_service_stats': locationStats,
      'last_check': DateTime.now().toIso8601String(),
    };
  }

  /// Clear all notification history (useful for testing)
  static void clearNotificationHistory() {
    TrainLocationNotificationService().clearNotificationHistory();

    if (kDebugMode) {
      print('🧹 Cleared all notification history');
    }
  }

  /// Test the integration with sample data
  static Future<bool> testIntegration({
    required NotificationTrayProvider trayProvider,
    String testTrainNumber = '12391',
    String testDate = '2025-06-02',
  }) async {
    try {
      if (kDebugMode) {
        print('🧪 Testing TrainLocationIntegrationService...');
      }

      final success = await fetchAndUpdateNotifications(
        trainNumber: testTrainNumber,
        date: testDate,
        trayProvider: trayProvider,
        enableProximityCheck: false, // Disable for testing
        showPushNotifications: true,
      );

      if (kDebugMode) {
        print('🧪 Integration test result: ${success ? 'SUCCESS' : 'FAILED'}');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Integration test failed: $e');
      }
      return false;
    }
  }

  /// Check if the service is properly configured
  static bool isConfigured() {
    // Check if all required services are available
    try {
      // This would check if Firebase is initialized, location permissions, etc.
      return true; // Simplified for now
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Service not properly configured: $e');
      }
      return false;
    }
  }

  /// Get current configuration status
  static Map<String, bool> getConfigurationStatus() {
    return {
      'firebase_initialized': true, // Would check actual Firebase status
      'location_permissions': true, // Would check actual location permissions
      'notification_permissions':
          true, // Would check actual notification permissions
      'api_connectivity': true, // Would check actual API connectivity
    };
  }
}
